<script setup lang="ts">
import { addAnswerApi, params2AddAns } from '@/apis/path/answerList';
import { useDialogStore } from '@/stores/dialog';
import { userInfoStore } from '@/stores/userInfo';
import { answerItem, klgType, tagType, taskType, taskTypePlus } from '@/utils/type';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { computed, onMounted, reactive } from 'vue';
import { ref, watch } from 'vue';
import InlineEditor from '@/components/editors/VditorInline.vue';
import { getKlgListApi } from '@/apis/path/prjdetail';
import { qType, qTypeDict } from '@/utils/constant';
import { getTagListApi, params2tList } from '@/apis/path/tagManage';
import { processAllLatexEquations, convertMathFormulas } from '@/utils/latexUtils';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import EditablePreview from '@/components/editors/EditablePreview.vue';

const dialogStore = useDialogStore();
const answerDialogVisible = ref(dialogStore.answerDialogVisible);
const question = ref(dialogStore.question);

const pageSize = ref(10);
const currentPage = ref(1);

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive({
  answer: '',
  list: []
});

const curAnswerKlg = ref<klgType[]>([]);

const curAnswer = reactive<answerItem>({
  answerExplanation: '',
  answerId: -1,
  answerStatus: -1,
  createTime: '',
  keyword: '',
  klgNumber: 0,
  title: '',
  type: 0,
  questionType: '',
  taskNumber: 0,
  questionDescription: ''
});

const kList = ref([]);
const loading = ref(false);

const userInfo = userInfoStore();
const username = userInfo.getUsername();

const isAddTagShow = ref<boolean>(true);

const tagList = ref<tagType[]>([]);

// 计算未删除的任务数量
const tasksLength = computed(() => {
  return ruleForm.list.filter((task) => !task.deleted).length;
});

// 检查是否有未完成的任务
const hasIncompleteTask = computed(() => {
  return ruleForm.list.some(
    (task) => !task.deleted && (!task.klgName || !task.klgName.trim() || !task.areaCode)
  );
});

watch(
  () => tasksLength.value,
  (newValue) => {
    if (qTypeDict[question.value.questionType] === qType.what) {
      if (newValue === 1) {
        isAddTagShow.value = false;
      } else if (newValue === 0) {
        isAddTagShow.value = true;
      }
    }
  }
);

// 处理添加任务
const handleAddTask = () => {
  if (qTypeDict[question.value.questionType] === qType.what && tasksLength.value === 1) {
    ElMessage.info('最多只能添加一条');
    return;
  }
  const task = ref<taskTypePlus>({
    klgName: '',
    areaTitle: '',
    areaCode: '',
    taskStatus: 0,
    handlerName: '',
    feedback: null,
    oid: null,
    taskStyle: '',
    deleted: false
  });
  ruleForm.list.push(task.value);
};

const isAddTaskShow = ref<boolean>(true);

watch(
  () => curAnswerKlg.value.length,
  (newValue) => {
    if (qTypeDict[question.value.questionType] === qType.what) {
      if (newValue === 1) {
        isAddTaskShow.value = false;
      } else if (newValue === 0) {
        isAddTaskShow.value = true;
      }
    }
  }
);

watch(
  () => dialogStore.answerDialogVisible,
  (newVal: boolean) => {
    answerDialogVisible.value = newVal;
    console.log(dialogStore.question);
  },
  { immediate: true } // 立即执行一次以初始化值
);

watch(answerDialogVisible, (newVal) => {
  if (newVal !== dialogStore.answerDialogVisible) {
    dialogStore.answerDialogVisible = newVal;
  }
});

watch(
  () => dialogStore.question,
  (newVal) => {
    question.value = newVal;
  }
);

const generateKlgString = () => {
  return curAnswerKlg.value.map((item) => item.code).join('@@');
};

const handleAnsQuesSubmit = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const toAddTaskList = ref<taskType[]>([]);
      toAddTaskList.value = ruleForm.list.map((task) => {
        return {
          klgName: task.klgName,
          areaTitle: task.areaTitle,
          areaCode: task.areaCode
        };
      });
      const params = ref<params2AddAns>({
        questionId: question.value.questionId,
        answerKlgs: generateKlgString(),
        answerExplanation: ruleForm.answer,
        taskList: toAddTaskList.value
      });
      addAnswerApi(params.value).then((res) => {
        if (res.success) {
          curAnswerKlg.value.splice(0, curAnswerKlg.value.length);
          ruleForm.list.splice(0, ruleForm.list.length);
          ruleForm.answer = '';
          ElMessage.success('添加成功');
          emitter.emit(Event.REFRESH_QUESTION_LIST, true);
        } else {
          ElMessage.warning(res.message);
        }
      });
    }
  });
};

// 处理选择klg
const handleSelectKlg = (item: klgType) => {
  if (qTypeDict[question.value.questionType] === qType.what) {
    if (curAnswerKlg.value.length === 1) {
      ElMessage.error('最多选择1个标签');
      return;
    }
  }
  item.choose = !item.choose;
  const index = curAnswerKlg.value.findIndex((i: klgType) => i.code === item.code);
  if (index === -1) {
    curAnswerKlg.value.push(item);
  } else {
    curAnswerKlg.value.splice(index, 1);
  }
};

const remoteKlgMethod = (query: string) => {
  if (query) {
    loading.value = true;
    getKlgListApi(query, qTypeDict[question.value.questionType]).then((res) => {
      if (res.success) {
        loading.value = false;
        kList.value = res.data;
      }
    });
  } else {
    kList.value = [];
  }
};

const handleDelete = (aimId: string) => {
  const newKlg: klgType[] = curAnswerKlg.value.filter((item) => item.code != aimId);
  curAnswerKlg.value = newKlg;
};

const rule4answer = reactive<FormRules>({
  answer: [{ required: true, message: '请输入答案', trigger: 'blur' }],
  list: []
});

// 筛选选项
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    const params = ref<params2tList>({
      limit: pageSize.value,
      current: currentPage.value,
      keyword: query,
      status: 1
    });
    getTagListApi(params.value).then((res) => {
      // @ts-ignore
      if (res.success) {
        loading.value = false;
        tagList.value = res.data.list;
      }
    });
  } else {
    tagList.value = [];
  }
};

// 处理选择tag
const handleSelectTag = (tag: tagType, item: taskTypePlus) => {
  item.areaCode = tag.areaCode;
  item.areaTitle = tag.title;
};

// 处理删除任务
const handleDelTask = (task: taskType) => {
  // 使用findIndex找到task在数组中的索引
  const index = ruleForm.list.findIndex(
    (t) => t.areaTitle === task.areaTitle && t.klgName === task.klgName
  );
  // 如果找到了任务，就使用splice方法删除它
  if (index !== -1) {
    ruleForm.list.splice(index, 1);
  }
};

const handleClose = () => {
  curAnswerKlg.value.splice(0, curAnswerKlg.value.length);
  ruleForm.list.splice(0, ruleForm.list.length);
  ruleForm.answer = '';
  answerDialogVisible.value = false;
};

const handleConfirm = () => {
  // 检查是否有未完成的任务
  const hasIncompleteTask = ruleForm.list.some(
    (task) => !task.deleted && (!task.klgName || !task.klgName.trim() || !task.areaCode)
  );

  if (hasIncompleteTask) {
    ElMessage.error('请完善所有任务信息，任务名称和领域都不能为空');
    return;
  }

  handleAnsQuesSubmit();
  answerDialogVisible.value = false;
};
</script>
<template>
  <div class="answer-dialog-container">
    <el-dialog v-model="answerDialogVisible" width="910" top="90px">
      <template #header>
        <div class="header">
          <span>回答问题</span>
        </div>
      </template>
      <div v-if="question" class="main-wrapper">
        <el-form ref="ruleFormRef" :rules="rule4answer" :model="ruleForm">
          <div class="top">
            <div>
              <span style="font-size: 12px; font-weight: 700; color: #333333">{{
                question.userName
              }}</span
              ><span style="margin-left: 10px; font-size: 12px">的提问</span>
            </div>
            <div class="related-content-container" style="background-color: white; display: flex">
              <b class="ques-mark">【</b
              ><span
                style="max-width: 90%; overflow: hidden"
                class="questionList markdown-content"
                v-html="processAllLatexEquations(question.keyword)"
              ></span
              ><b class="ques-mark">】</b>
              <span class="q-type"
                >{{ question.questionType
                }}<span v-if="qTypeDict[question.questionType] !== qType.open">?</span></span
              >
            </div>
            <div v-if="qTypeDict[question.questionType] === qType.open">
              <span class="ques-decription">
                <span class="ck-content" v-html="question.questionDescription"></span>
              </span>
            </div>
            <div style="margin-bottom: 25px">
              <span style="font-size: 12px">{{ question.createTime }}</span>
            </div>
          </div>
          <div class="middle">
            <el-form-item>
              <div style="margin-top: 15px">
                <span style="font-weight: 600; font-size: 12px; color: #333333">{{ username }}</span
                ><span style="margin-left: 10px; font-size: 12px">的回答</span>
              </div>
            </el-form-item>
            <div class="answer-area">
              <el-form-item prop="answer">
                <div
                  :class="
                    qTypeDict[question.questionType] !== qType.open ? 'input-area' : 'input-area2'
                  "
                >
                  <InlineEditor
                    v-model="ruleForm.answer"
                    style="width: 100%"
                    :height="500"
                    :showToolbar="true"
                  >
                  </InlineEditor>
                </div>
              </el-form-item>
              <div class="klg-list">
                <el-form-item
                  v-if="qTypeDict[question.questionType] !== qType.open && isAddTagShow"
                >
                  <div style="width: 100%; padding: 10px">
                    <span>请把答案中的知识点选出来:</span>
                    <div style="margin-top: 10px">
                      <el-select
                        v-if="qTypeDict[curAnswer.questionType] !== qType.what"
                        multiple
                        filterable
                        suffix-icon="Search"
                        :fit-input-width="true"
                        remote
                        reserve-keyword
                        placeholder="请输入知识名称"
                        placement="top"
                        :remote-method="remoteKlgMethod"
                        :loading="loading"
                        collapse-tags
                        :remote-show-suffix="true"
                        :max-collapse-tags="0"
                      >
                        <template #suffix-icon>
                          <el-icon>
                            <Search />
                          </el-icon>
                        </template>
                        <el-option
                          v-for="item in kList"
                          :key="item.code"
                          :value="item.title"
                          :class="item.choose ? 'highlight' : ''"
                          style="width: 100%"
                          @click="handleSelectKlg(item)"
                        >
                          <!-- 该实现出现了bug，导致后边的v-html不渲染 -->
                          <div style="display: inline">
                            <span class="option-tag" :class="`option-tag${item.type}`"
                              >{{ item.type === 2 ? '领域' : '知识' }}
                            </span>
                          </div>
                          <div style="display: inline">
                            <span class="ck-content" v-html="item.title" id="p_inline"></span>
                          </div>
                        </el-option>
                      </el-select>
                      <el-select
                        v-else
                        filterable
                        :fit-input-width="true"
                        suffix-icon="Search"
                        remote
                        reserve-keyword
                        placeholder="请输入知识名称"
                        placement="top"
                        :remote-method="remoteKlgMethod"
                        :remote-show-suffix="true"
                        :loading="loading"
                      >
                        <template #suffix-icon>
                          <el-icon>
                            <Search />
                          </el-icon>
                        </template>
                        <el-option
                          v-for="item in kList"
                          :key="item.code"
                          :label="item.title"
                          :value="item.title"
                          :class="item.choose ? 'highlight' : ''"
                          style="width: 100%"
                          @click="handleSelectKlg(item)"
                        >
                          <span class="option-tag" :class="`option-tag${item.type}`">{{
                            item.type === 2 ? '领域' : '知识'
                          }}</span>
                          <span class="ck-content" v-html="item.title"></span>
                        </el-option>
                      </el-select>
                    </div>
                    <div class="selected-list" id="bottom">
                      <my-tag
                        class="klg"
                        @delete="handleDelete"
                        :tag-id="item.code"
                        v-for="item in curAnswerKlg"
                        :key="item.code"
                        type="newTarget"
                      >
                        <el-tooltip raw-content popper-class="tooltip-width">
                          <template #content>
                            <div class="tooltipHtml ck-content" v-html="item.title"></div>
                          </template>
                          <span class="htmlContent2 ck-content" v-html="item.title"></span>
                        </el-tooltip>
                      </my-tag>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item
                  v-if="
                    isAddTagShow && isAddTaskShow && qTypeDict[question.questionType] !== qType.open
                  "
                >
                  <div class="line"></div>
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="bottom">
            <el-form-item v-if="qTypeDict[question.questionType] !== qType.open && isAddTaskShow">
              <div style="width: 100%">
                <div class="add-klg" style="width: 100%; margin: 10px 0">
                  <span> 如果没有找到合适的知识点,请点这里 </span>
                  <span class="icon" @click="handleAddTask">
                    <img style="margin-top: 4px" src="@/assets/images/answerlist/u736.svg" />
                  </span>
                </div>
              </div>
            </el-form-item>
            <el-form-item
              v-if="qTypeDict[question.questionType] !== qType.open && isAddTaskShow"
              v-for="(item, index) in ruleForm.list"
              :key="item.areaCode"
            >
              <div v-if="!item.deleted" class="task-list">
                <el-col :span="16">
                  <el-form-item
                    :prop="`list.${index}.klgName`"
                    :rules="{
                      required: true,
                      message: '请输入任务名称',
                      trigger: 'blur'
                    }"
                    style="width: 100%"
                  >
                    <!-- 编辑模式：显示输入框 -->
                    <EditablePreview
                      v-if="!item.showPreview"
                      v-model="item.klgName"
                      placeholder="请输入知识名称，支持数学公式"
                      width="100%"
                      height="30px"
                      :maxlength="128"
                      :show-word-limit="true"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item
                    :prop="`list.${index}.areaTitle`"
                    :rules="{
                      required: true,
                      message: '请选择领域',
                      trigger: 'blur'
                    }"
                    style="width: 100%"
                  >
                    <el-select
                      v-model="item.areaCode"
                      filterable
                      :fit-input-width="true"
                      suffix-icon="Search"
                      clearable
                      remote
                      reserve-keyword
                      placeholder="选择领域"
                      :remote-method="remoteMethod"
                      :remote-show-suffix="true"
                      :loading="loading"
                      :validate-event="true"
                      style="width: 100%"
                    >
                      <template #suffix-icon>
                        <el-icon>
                          <Search />
                        </el-icon>
                      </template>
                      <el-option
                        v-for="tag in tagList"
                        :key="tag.title"
                        :label="tag.title"
                        :value="tag.areaCode"
                        v-html="tag.title"
                        class="ck-content"
                        :class="item.areaCode === tag.areaCode ? 'highlight' : ''"
                        style="width: 100%"
                        @click="handleSelectTag(tag, item)"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <span class="icon" @click="handleDelTask(item)">
                  <img src="@/assets/images/answerlist/u742.svg" />
                </span>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="handleConfirm"
            style="width: 120px; height: 35px"
            :disabled="hasIncompleteTask"
          >
            提交
          </el-button>
          <el-button @click="handleClose" style="width: 120px; height: 35px">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style scoped lang="less">

:deep(.el-form-item) {
  margin-bottom: 0;
}
.header {
  width: 880px;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
  padding-bottom: 10px;
  border-bottom: 1px solid #dcdfe6;
}

.main-wrapper {
  width: 850px;
  margin: 0 auto;

  .top {
    border-bottom: 1px solid #dcdfe6;

    .related-content-container {
      display: flex;
      align-items: center;
      margin: 12px 0;
      background-color: var(--color-light);
      word-break: break-all;

      .ques-decription {
        display: flex;
        text-align: center;
        min-width: 100%;
        border: 1px solid var(--color-primary);
        border-radius: 5px;
        padding: 10px;
      }

      .ques-username {
        margin-right: 10px;
      }

      .ques-mark {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 600;
        color: #333333;
      }

      .questionList {
        font-size: 14px;
        font-weight: 600;
        color: #333333;
      }

      .q-type {
        display: inline-block;
        font-weight: bold;
        //padding-left: 10px;
        white-space: nowrap;
      }
    }
  }

  .middle {
    width: 100%;

    .answer-area {
      margin-top: 0px;
      width: 100%;
      display: flex;
      gap: 10px;
      margin-bottom: 18px;

      .input-area {
        width: 490px;
        height: 500px;
      }

      .input-area2 {
        width: 830px;
        height: 500px;
      }

      .klg-list {
        width: 340px;
        height: 500px;

        .option-tag {
          margin-right: 10px;
          border: 1px solid black;
          padding: 0 5px;
          border-radius: 3px;
          font-weight: 400 !important;
        }

        .selected-list {
          display: flex;
          flex-wrap: wrap;
          margin-top: 10px;
          width: 340px;
          max-height: 400px;
          overflow: auto;

          .klg {
            width: 300px;
            padding-left: 10px;
            margin-bottom: 5px;
            border-radius: 4px;

            .htmlContent2 {
              display: inline-block;
              max-width: 280px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              vertical-align: middle;
            }
          }
        }
      }
    }
  }

  .bottom {
    border-top: 1px solid #dcdfe6;
    width: 100%;

    .task-list {
      width: 100%;
      display: flex;
      margin-bottom: 10px;
      justify-content: space-between;

      .select-area {
        margin-left: 10px;
        width: 35%;
      }

      .icon {
        margin-left: 10px;
        display: flex;
        align-items: center;

        &:hover {
          cursor: pointer;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: center;
}
</style>
